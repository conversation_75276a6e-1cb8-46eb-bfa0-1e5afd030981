package com.example.zuijiji2

import android.net.Uri
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.example.zuijiji2.ui.theme.Zuijiji2Theme
import kotlin.math.sqrt

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            Zuijiji2Theme {
                MainScreen()
            }
        }
    }
}

@Composable
fun MainScreen() {
    var backgroundImageUri by remember { mutableStateOf<Uri?>(null) }
    var isMagnifierVisible by remember { mutableStateOf(false) }
    var magnifierPosition by remember { mutableStateOf(Offset(200f, 200f)) }

    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        backgroundImageUri = uri
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // Background image
        backgroundImageUri?.let { uri ->
            AsyncImage(
                model = uri,
                contentDescription = "Background Image",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
        }

        // Import image button (bottom left)
        FloatingActionButton(
            onClick = { imagePickerLauncher.launch("image/*") },
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(16.dp),
            containerColor = MaterialTheme.colorScheme.primary
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_image_import),
                contentDescription = "Import Image",
                tint = MaterialTheme.colorScheme.onPrimary
            )
        }

        // Magnifier button (bottom right)
        FloatingActionButton(
            onClick = { isMagnifierVisible = !isMagnifierVisible },
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(16.dp),
            containerColor = MaterialTheme.colorScheme.secondary
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_magnifier),
                contentDescription = "Magnifier",
                tint = MaterialTheme.colorScheme.onSecondary
            )
        }

        // Draggable magnifier
        if (isMagnifierVisible) {
            DraggableMagnifier(
                position = magnifierPosition,
                onPositionChange = { magnifierPosition = it },
                backgroundImageUri = backgroundImageUri
            )
        }
    }
}

@Composable
fun DraggableMagnifier(
    position: Offset,
    onPositionChange: (Offset) -> Unit,
    backgroundImageUri: Uri?,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    val magnifierSize = 120.dp
    val magnifierSizePx = with(density) { magnifierSize.toPx() }

    Box(
        modifier = modifier
            .offset(
                x = with(density) { (position.x - magnifierSizePx / 2).toDp() },
                y = with(density) { (position.y - magnifierSizePx / 2).toDp() }
            )
            .size(magnifierSize)
            .pointerInput(Unit) {
                detectDragGestures { change, _ ->
                    onPositionChange(
                        Offset(
                            x = (position.x + change.x).coerceIn(
                                magnifierSizePx / 2,
                                size.width - magnifierSizePx / 2
                            ),
                            y = (position.y + change.y).coerceIn(
                                magnifierSizePx / 2,
                                size.height - magnifierSizePx / 2
                            )
                        )
                    )
                }
            }
    ) {
        // Magnifier circle with transparent center
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape)
        ) {
            // Draw semi-transparent overlay
            drawCircle(
                color = Color.Black.copy(alpha = 0.3f),
                radius = size.minDimension / 2,
                center = center
            )

            // Draw transparent center (magnified area)
            drawCircle(
                color = Color.Transparent,
                radius = size.minDimension / 4,
                center = center,
                blendMode = BlendMode.Clear
            )

            // Draw border
            drawCircle(
                color = Color.White,
                radius = size.minDimension / 2,
                center = center,
                style = androidx.compose.ui.graphics.drawscope.Stroke(width = 4.dp.toPx())
            )
        }

        // Magnified content
        backgroundImageUri?.let { uri ->
            Box(
                modifier = Modifier
                    .size(60.dp)
                    .align(Alignment.Center)
                    .clip(CircleShape)
            ) {
                AsyncImage(
                    model = uri,
                    contentDescription = "Magnified Image",
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(CircleShape),
                    contentScale = ContentScale.Crop
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    Zuijiji2Theme {
        MainScreen()
    }
}